import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Radio,
  Input,
  InputNumber,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  message,
  Spin,
  Modal,
  Divider,
  Alert,
  Select,
  Tooltip
} from 'antd';
import {
  ShareAltOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  ReloadOutlined,
  ClearOutlined,
  SaveOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DatabaseOutlined,
  CloudOutlined,
  CodeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CloudServerOutlined
} from '@ant-design/icons';
import graphEnhancementAPI from '../../services/api/graphEnhancement';
import { modelConfigAPI } from '../../services/api/model';
import tidbVectorAPI from '../../services/api/tidbVector';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const GraphEnhancementSettingsPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState(null);
  const [status, setStatus] = useState(null);
  const [localEnabled, setLocalEnabled] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState('graphiti');

  const [testResult, setTestResult] = useState(null);
  const [queryModalVisible, setQueryModalVisible] = useState(false);

  const [clearLoading, setClearLoading] = useState(false);

  // 新增状态
  const [modelConfigs, setModelConfigs] = useState([]);
  const [textModels, setTextModels] = useState([]);
  const [embeddingModels, setEmbeddingModels] = useState([]);
  const [defaultTextModel, setDefaultTextModel] = useState(null);
  const [defaultEmbeddingModel, setDefaultEmbeddingModel] = useState(null);

  const [databaseType, setDatabaseType] = useState('neo4j');
  const [mcpModalVisible, setMcpModalVisible] = useState(false);

  // 加载配置
  const loadConfig = async () => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.getConfig();
      
      if (result.success) {
        const configData = result.data || {};
        setConfig(configData);
        setLocalEnabled(configData.enabled || false);
        setSelectedFramework(configData.framework || 'graphiti');

        // 设置模型类型和数据库类型
        const dbType = configData.framework_config?.database_type || 'neo4j';
        setDatabaseType(dbType);

        // 设置默认值
        const formValues = {
          ...configData,
          framework_config: {
            // Graphiti默认配置
            database_type: 'neo4j',
            // Neo4j配置
            neo4j_uri: 'bolt://localhost:7687',
            neo4j_user: 'neo4j',
            neo4j_password: '',
            database_name: 'neo4j',
            // FalkorDB配置
            falkordb_host: 'localhost',
            falkordb_port: 6379,
            falkordb_username: '',
            falkordb_password: '',
            falkordb_graph_name: 'default_graph',
            text_model_type: 'default',
            text_model_id: 'default',
            openai_compatible: false,
            embedding_model_type: 'default',
            embedding_model_id: 'default',
            embedding_dimension: 1024,
            memory_partition_strategy: 'by_task',
            max_nodes_per_partition: 10000,
            importance_threshold: 0.3,
            enable_time_decay: true,
            use_namespaces: false,
            ...configData.framework_config
          }
        };

        form.setFieldsValue(formValues);
      } else {
        message.error(result.message || '获取配置失败');
      }
    } catch (error) {
      message.error('获取配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 加载状态
  const loadStatus = async () => {
    try {
      const result = await graphEnhancementAPI.getStatus();
      if (result.success) {
        setStatus(result.data);
      }
    } catch (error) {
      console.error('获取状态失败:', error);
    }
  };

  // 加载模型配置
  const loadModelConfigs = async () => {
    try {
      const configs = await modelConfigAPI.getAll();
      setModelConfigs(configs);

      // 分离文本生成模型和嵌入模型
      const textModelList = configs.filter(model =>
        model.modalities && model.modalities.includes('text_output')
      );
      setTextModels(textModelList);

      // 获取默认文本生成模型
      const defaultText = configs.find(model => model.is_default_text);
      setDefaultTextModel(defaultText?.id || null);

      // 获取嵌入模型列表
      const embeddingResult = await tidbVectorAPI.getEmbeddingModels();
      if (embeddingResult.success) {
        setEmbeddingModels(embeddingResult.models);
        setDefaultEmbeddingModel(embeddingResult.default_model_id);
      }
    } catch (error) {
      console.error('加载模型配置失败:', error);
    }
  };

  useEffect(() => {
    loadConfig();
    loadStatus();
    loadModelConfigs();
  }, []);

  // 保存配置
  const handleSaveConfig = async (values) => {
    try {
      setLoading(true);

      // 处理模型配置 - 保存完整的模型信息到framework_config
      let processedValues = { ...values };

      try {
        // 处理文本生成模型配置
        const textModelId = values.framework_config?.text_model_id;
        if (textModelId && textModelId !== 'default') {
          const textModel = textModels.find(m => m.id.toString() === textModelId.toString());
          if (textModel) {
            const modelDetail = await modelConfigAPI.getById(textModel.id, true);

            // 保存完整的文本模型信息
            processedValues.framework_config.text_model = {
              id: modelDetail.id,
              name: modelDetail.name,
              provider: modelDetail.provider,
              model_id: modelDetail.model_id,
              api_key: modelDetail.api_key || 'no-api-key',
              base_url: modelDetail.base_url || null
            };

            console.log(`保存文本生成模型: ${modelDetail.name}, API密钥: ${modelDetail.api_key ? '已设置' : '使用no-api-key'}`);
          }
        } else {
          // 使用默认文本生成模型
          const defaultTextModel = textModels.find(m => m.is_default_text);
          if (defaultTextModel) {
            const modelDetail = await modelConfigAPI.getById(defaultTextModel.id, true);
            processedValues.framework_config.text_model = {
              id: modelDetail.id,
              name: modelDetail.name,
              provider: modelDetail.provider,
              model_id: modelDetail.model_id,
              api_key: modelDetail.api_key || 'no-api-key',
              base_url: modelDetail.base_url || null
            };
            console.log(`使用默认文本生成模型: ${modelDetail.name}`);
          }
        }

        // 处理嵌入模型配置
        const embeddingModelId = values.framework_config?.embedding_model_id;
        if (embeddingModelId && embeddingModelId !== 'default') {
          const embeddingModel = embeddingModels.find(m => m.id.toString() === embeddingModelId.toString());
          if (embeddingModel) {
            const modelDetail = await modelConfigAPI.getById(embeddingModel.id, true);

            // 保存完整的嵌入模型信息
            processedValues.framework_config.embedding_model = {
              id: modelDetail.id,
              name: modelDetail.name,
              provider: modelDetail.provider,
              model_id: modelDetail.model_id,
              api_key: modelDetail.api_key || 'no-api-key',
              base_url: modelDetail.base_url || null
            };

            console.log(`保存嵌入模型: ${modelDetail.name}, API密钥: ${modelDetail.api_key ? '已设置' : '使用no-api-key'}`);
          }
        } else {
          // 使用默认嵌入模型
          const defaultEmbeddingModel = embeddingModels.find(m => m.is_default_embedding);
          if (defaultEmbeddingModel) {
            const modelDetail = await modelConfigAPI.getById(defaultEmbeddingModel.id, true);
            processedValues.framework_config.embedding_model = {
              id: modelDetail.id,
              name: modelDetail.name,
              provider: modelDetail.provider,
              model_id: modelDetail.model_id,
              api_key: modelDetail.api_key || 'no-api-key',
              base_url: modelDetail.base_url || null
            };
            console.log(`使用默认嵌入模型: ${modelDetail.name}`);
          }
        }
      } catch (error) {
        console.error('处理模型配置时出错:', error);
        message.error('处理模型配置失败: ' + error.message);
        return;
      }

      const configData = {
        ...processedValues,
        enabled: localEnabled
      };

      const result = await graphEnhancementAPI.saveConfig(configData);

      if (result.success) {
        message.success('配置保存成功');
        setConfig(configData);
        loadStatus();
      } else {
        message.error(result.message || '配置保存失败');
      }
    } catch (error) {
      message.error('配置保存失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };





  // 服务控制
  const handleServiceControl = async (action) => {
    try {
      setLoading(true);

      // 验证操作类型
      if (!['start', 'stop'].includes(action)) {
        message.error('未知的服务操作');
        return;
      }

      const actionText = {
        'start': '启动',
        'stop': '停止'
      }[action];

      message.info(`正在${actionText}Graphiti服务...`);

      // 准备请求数据
      let requestData = { action };

      const result = await graphEnhancementAPI.controlService(requestData);

      if (result.success) {
        message.success(`Graphiti服务${actionText}成功`);
        // 刷新状态
        loadStatus();
      } else {
        throw new Error(result.message);
      }

    } catch (error) {
      console.error('服务控制失败:', error);
      message.error(`服务控制失败: ${error.message || '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试查询
  const handleTestQuery = async (queryData) => {
    try {
      setLoading(true);
      const result = await graphEnhancementAPI.testQuery(queryData);

      if (result.success) {
        setTestResult(result.data);
        message.success('查询测试成功');
      } else {
        message.error(result.message || '查询测试失败');
        setTestResult(null);
      }
    } catch (error) {
      message.error('查询测试失败: ' + error.message);
      setTestResult(null);
    } finally {
      setLoading(false);
    }
  };



  // 清空数据
  const handleClearData = async () => {
    Modal.confirm({
      title: '确认清空数据',
      content: '此操作将清空所有图谱数据，无法恢复。确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setClearLoading(true);
          const result = await graphEnhancementAPI.clearGraph();

          if (result.success) {
            message.success('数据清空成功');
            loadStatus();
          } else {
            message.error(result.message || '数据清空失败');
          }
        } catch (error) {
          message.error('数据清空失败: ' + error.message);
        } finally {
          setClearLoading(false);
        }
      }
    });
  };

  const renderFrameworkDescription = (framework) => {
    const descriptions = {
      graphiti: '时序感知知识图谱，支持分区记忆管理，企业级功能（推荐）',
      lightrag: '简单快速，支持多种查询模式，适合快速部署',
      graphrag: '微软方案，适合文档分析'
    };
    return descriptions[framework] || '';
  };

  const renderStatusTag = (status) => {
    const statusConfig = {
      connected: { color: 'green', text: '已连接' },
      ready: { color: 'green', text: '服务正常' },
      disconnected: { color: 'red', text: '未连接' },
      error: { color: 'red', text: '错误' },
      initializing: { color: 'blue', text: '初始化中' }
    };

    const config = statusConfig[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading && !config) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="graph-enhancement-settings-container">
      <div style={{ marginBottom: '24px' }}>
        <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>
          <ShareAltOutlined style={{ marginRight: '8px' }} />
          图谱增强设置
        </Title>
        <Text type="secondary">
          配置图谱增强功能，提升知识库检索和会话的准确性与上下文理解能力
        </Text>
      </div>

      {/* 顶部开关区域 */}
      <Card
        title="启用状态"
        style={{ marginBottom: 24 }}
      >
        <Row align="middle" gutter={16}>
          <Col>
            <Switch
              checked={localEnabled}
              checkedChildren="启用"
              unCheckedChildren="禁用"
              loading={loading}
              onChange={async (checked) => {
                setLocalEnabled(checked);
                // 自动保存开关状态
                try {
                  setLoading(true);
                  const currentConfig = config || {};
                  const configData = {
                    ...currentConfig,
                    enabled: checked
                  };

                  const result = await graphEnhancementAPI.saveConfig(configData);

                  if (result.success) {
                    message.success(checked ? '图谱增强已启用' : '图谱增强已禁用');
                    setConfig(configData);
                    loadStatus();
                  } else {
                    message.error(result.message || '状态保存失败');
                    // 如果保存失败，恢复原状态
                    setLocalEnabled(!checked);
                  }
                } catch (error) {
                  message.error('状态保存失败: ' + error.message);
                  // 如果保存失败，恢复原状态
                  setLocalEnabled(!checked);
                } finally {
                  setLoading(false);
                }
              }}
            />
          </Col>
          <Col flex={1}>
            <Text type="secondary">
              图谱增强可以通过知识图谱技术提升检索准确性和上下文理解能力，作用于知识库与会话两套系统
            </Text>
          </Col>
        </Row>
      </Card>

      {localEnabled && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveConfig}
          initialValues={config}
        >
          {/* 框架选择和状态监控 - 同一行两个卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            {/* RAG框架选择卡片 */}
            <Col span={12}>
              <Card
                title={
                  <Space>
                    <SettingOutlined />
                    RAG框架选择
                  </Space>
                }
                style={{ height: '100%' }}
              >
                <Form.Item name="framework" label="选择框架">
                  <Radio.Group onChange={(e) => setSelectedFramework(e.target.value)}>
                    <Space direction="vertical">
                      <Radio value="graphiti">
                        <Space>
                          <ShareAltOutlined style={{ color: '#1890ff' }} />
                          <strong>Graphiti</strong>
                          <Tag color="blue" size="small">推荐</Tag>
                          <Text type="secondary">
                            {renderFrameworkDescription('graphiti')}
                          </Text>
                        </Space>
                      </Radio>
                      <Radio value="lightrag" disabled>
                        <Space>
                          <SettingOutlined style={{ color: '#d9d9d9' }} />
                          <strong style={{ color: '#d9d9d9' }}>LightRAG</strong>
                          <Tag color="default" size="small">暂不可用</Tag>
                          <Text type="secondary" style={{ color: '#d9d9d9' }}>
                            {renderFrameworkDescription('lightrag')}
                          </Text>
                        </Space>
                      </Radio>
                      <Radio value="graphrag" disabled>
                        <Space>
                          <BarChartOutlined style={{ color: '#d9d9d9' }} />
                          <strong style={{ color: '#d9d9d9' }}>GraphRAG</strong>
                          <Tag color="default" size="small">暂不可用</Tag>
                          <Text type="secondary" style={{ color: '#d9d9d9' }}>
                            {renderFrameworkDescription('graphrag')}
                          </Text>
                        </Space>
                      </Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
              </Card>
            </Col>

            {/* 图谱状态监控卡片 */}
            <Col span={12}>
              {status ? (
                <Card
                  title={
                    <Space>
                      <BarChartOutlined />
                      图谱状态监控
                    </Space>
                  }
                  style={{ height: '100%' }}
                  extra={
                    <Button
                      size="small"
                      onClick={loadStatus}
                      icon={<ReloadOutlined />}
                    >
                      刷新状态
                    </Button>
                  }
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <div>
                        <Text strong>连接状态:</Text>{' '}
                        {renderStatusTag(status.status)}
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <Text strong>框架:</Text> {status.framework}
                      </div>
                    </Col>
                  </Row>
                  <Row gutter={16} style={{ marginTop: 8 }}>
                    <Col span={12}>
                      <div>
                        <Text strong>节点数量:</Text> {status.statistics?.node_count || status.statistics?.entity_count || 0}
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <Text strong>关系数量:</Text> {status.statistics?.relation_count || 0}
                      </div>
                    </Col>
                  </Row>

                  {/* Graphiti特有的状态信息 */}
                  {status.framework === 'graphiti' && (
                    <>
                      <Divider style={{ margin: '12px 0' }} />
                      <Row gutter={16}>
                        <Col span={12}>
                          <div>
                            <Text strong>分区数量:</Text> {status.statistics?.partition_count || 0}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div>
                            <Text strong>数据库类型:</Text> {status.database_type || 'Unknown'}
                          </div>
                        </Col>
                      </Row>
                      <Row gutter={16} style={{ marginTop: 8 }}>
                        <Col span={12}>
                          <div>
                            <Text strong>嵌入模型:</Text> {status.embedding_model || 'Default'}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div>
                            <Text strong>最后更新:</Text> {status.last_updated ? new Date(status.last_updated).toLocaleString() : 'Unknown'}
                          </div>
                        </Col>
                      </Row>
                    </>
                  )}
                </Card>
              ) : (
                <Card
                  title={
                    <Space>
                      <BarChartOutlined />
                      图谱状态监控
                    </Space>
                  }
                  style={{ height: '100%' }}
                >
                  <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
                    <Text type="secondary">暂无状态信息</Text>
                  </div>
                </Card>
              )}
            </Col>
          </Row>

          {/* 框架配置区域 */}
          {selectedFramework === 'lightrag' && (
            <Card
              title="LightRAG 配置"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'embedding_model']} label="嵌入模型">
                    <Input placeholder="text-embedding-ada-002" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'llm_model']} label="LLM模型">
                    <Input placeholder="gpt-4" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'chunk_size']} label="文档块大小">
                    <InputNumber min={100} max={2000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'chunk_overlap']} label="文档块重叠">
                    <InputNumber min={0} max={500} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {selectedFramework === 'graphiti' && (
            <Card
              title={
                <Space>
                  <DatabaseOutlined />
                  Graphiti 配置
                  <Tag color="blue" size="small">时序感知知识图谱</Tag>
                </Space>
              }
              style={{ marginBottom: 24 }}
            >


              {/* 数据库连接和LLM嵌入配置 - 同一行两个卡片 */}
              <Row gutter={16} style={{ marginBottom: 16 }}>
                {/* 图数据库连接卡片 */}
                <Col span={12}>
                  <Card
                    type="inner"
                    title={
                      <Space>
                        <DatabaseOutlined />
                        图数据库连接
                        <Tooltip title="Graphiti支持Neo4j 5.26+和FalkorDB 1.1.2+">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </Space>
                    }
                    style={{ height: '100%' }}
                  >
                    <Form.Item
                      name={['framework_config', 'database_type']}
                      label="数据库类型"
                      initialValue="neo4j"
                      rules={[{ required: true, message: '请选择数据库类型' }]}
                      style={{ marginBottom: 16 }}
                    >
                      <Select
                        placeholder="选择图数据库类型"
                        onChange={(value) => setDatabaseType(value)}
                      >
                        <Select.Option value="neo4j">
                          <Space>
                            <DatabaseOutlined />
                            Neo4j (推荐)
                            <Tag color="blue" size="small">完整功能</Tag>
                          </Space>
                        </Select.Option>
                        <Select.Option value="falkordb">
                          <Space>
                            <CloudOutlined />
                            FalkorDB
                            <Tag color="green" size="small">轻量级</Tag>
                          </Space>
                        </Select.Option>
                      </Select>
                    </Form.Item>

                    {/* Neo4j 配置 */}
                    {databaseType === 'neo4j' && (
                      <>
                        <Form.Item
                          name={['framework_config', 'neo4j_uri']}
                          label="连接URI"
                          rules={[{ required: true, message: '请输入连接URI' }]}
                          tooltip="Neo4j连接地址，如: bolt://localhost:7687 或 neo4j+s://xxx.databases.neo4j.io (AuraDB)"
                          style={{ marginBottom: 12 }}
                        >
                          <Input placeholder="bolt://localhost:7687" />
                        </Form.Item>
                        <Form.Item
                          name={['framework_config', 'database_name']}
                          label="数据库名"
                          tooltip="Neo4j数据库名称，默认为'neo4j'"
                          style={{ marginBottom: 12 }}
                        >
                          <Input placeholder="neo4j" />
                        </Form.Item>
                        <Row gutter={8}>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'neo4j_user']}
                              label="用户名"
                              rules={[{ required: true, message: '请输入用户名' }]}
                              style={{ marginBottom: 0 }}
                            >
                              <Input placeholder="neo4j" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'neo4j_password']}
                              label="密码"
                              rules={[{ required: true, message: '请输入密码' }]}
                              style={{ marginBottom: 0 }}
                            >
                              <Input.Password placeholder="password" />
                            </Form.Item>
                          </Col>
                        </Row>
                      </>
                    )}

                    {/* FalkorDB 配置 */}
                    {databaseType === 'falkordb' && (
                      <>
                        <Row gutter={8}>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'falkordb_host']}
                              label="主机地址"
                              rules={[{ required: true, message: '请输入主机地址' }]}
                              tooltip="FalkorDB是Redis的图数据库模块，通常不需要用户名密码认证。请输入FalkorDB服务器地址"
                              style={{ marginBottom: 12 }}
                            >
                              <Input placeholder="localhost" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'falkordb_port']}
                              label="端口"
                              rules={[{ required: true, message: '请输入端口' }]}
                              tooltip="FalkorDB服务端口，默认6379"
                              style={{ marginBottom: 12 }}
                            >
                              <InputNumber
                                placeholder="6379"
                                min={1}
                                max={65535}
                                style={{ width: '100%' }}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Row gutter={8}>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'falkordb_username']}
                              label="用户名（可选）"
                              tooltip="FalkorDB用户名，通常可以留空"
                              style={{ marginBottom: 12 }}
                            >
                              <Input placeholder="留空表示无用户名" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              name={['framework_config', 'falkordb_password']}
                              label="密码（可选）"
                              tooltip="如果FalkorDB设置了密码认证，请填写"
                              style={{ marginBottom: 12 }}
                            >
                              <Input.Password placeholder="留空表示无密码" />
                            </Form.Item>
                          </Col>
                        </Row>
                        <Form.Item
                          name={['framework_config', 'falkordb_graph_name']}
                          label="图名称"
                          tooltip="FalkorDB中的图名称，默认为'default_graph'"
                          rules={[{ required: true, message: '请输入图名称' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Input placeholder="default_graph" />
                        </Form.Item>
                      </>
                    )}
                  </Card>
                </Col>

                {/* LLM和嵌入模型配置卡片 */}
                <Col span={12}>
                  <Card
                    type="inner"
                    title={
                      <Space>
                        <CloudOutlined />
                        LLM和嵌入模型配置
                        <Tooltip title="Graphiti需要两种模型：1) 文本生成模型用于LLM推理和知识提取；2) 嵌入模型用于生成向量表示。请确保已在系统中正确配置了相应的模型。">
                          <InfoCircleOutlined style={{ color: '#1890ff' }} />
                        </Tooltip>
                      </Space>
                    }
                    style={{ height: '100%' }}
                  >


                    {/* 文本生成模型 */}
                    <Form.Item
                      name={['framework_config', 'text_model_id']}
                      label={
                        <Space>
                          <CloudOutlined style={{ color: '#1677ff' }} />
                          <span>文本生成模型</span>
                        </Space>
                      }
                      tooltip="用于LLM推理和知识提取的模型"
                      initialValue="default"
                      rules={[{ required: true, message: '请选择文本生成模型' }]}
                      style={{ marginBottom: 16 }}
                    >
                      <Select
                        placeholder="选择文本生成模型"
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          option?.label?.toLowerCase().includes(input.toLowerCase())
                        }
                        style={{ borderRadius: '6px' }}
                        options={[
                          // 默认模型选项
                          {
                            value: 'default',
                            label: `默认文本生成模型${defaultTextModel ? ` (${textModels.find(m => m.id === defaultTextModel)?.name || 'Unknown'})` : ''}`,
                            isDefault: true,
                            model: defaultTextModel ? textModels.find(m => m.id === defaultTextModel) : null
                          },
                          // 其他模型选项
                          ...(textModels && textModels.length > 0 ?
                            textModels.map(model => ({
                              value: model.id.toString(),
                              label: `${model.name} (${model.provider})`,
                              isDefault: false,
                              model: model
                            })) : []
                          )
                        ]}
                        optionRender={(option) => {
                          if (option.data.isDefault) {
                            return (
                              <div>
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                  <span style={{ fontWeight: 'bold' }}>默认文本生成模型</span>
                                  <Tag color="blue" size="small">默认</Tag>
                                </div>
                                {option.data.model && (
                                  <div style={{ fontSize: '12px', color: '#666' }}>
                                    {option.data.model.provider} - {option.data.model.model_id}
                                  </div>
                                )}
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <div style={{ fontWeight: 'bold' }}>{option.data.model.name}</div>
                                <div style={{ fontSize: '12px', color: '#666' }}>
                                  {option.data.model.provider} - {option.data.model.model_id}
                                </div>
                              </div>
                            );
                          }
                        }}
                      />
                    </Form.Item>

                    {/* 宽松解析开关 */}
                    <Form.Item
                      name={['framework_config', 'openai_compatible']}
                      label={
                        <Space>
                          <SettingOutlined style={{ color: '#1677ff' }} />
                          <span>宽松解析</span>
                          <Tooltip title="启用此选项可以提高与非OpenAI标准LLM提供商的兼容性，如国产大模型（智谱、阿里云、百度等）。如果遇到响应格式验证错误，请尝试启用此选项。">
                            <InfoCircleOutlined style={{ color: '#1890ff' }} />
                          </Tooltip>
                        </Space>
                      }
                      valuePropName="checked"
                      initialValue={false}
                      style={{ marginBottom: 16 }}
                    >
                      <Switch />
                    </Form.Item>

                    {/* 嵌入模型 */}
                    <Form.Item
                      name={['framework_config', 'embedding_model_id']}
                      label={
                        <Space>
                          <CloudOutlined style={{ color: '#52c41a' }} />
                          <span>嵌入模型</span>
                        </Space>
                      }
                      tooltip="用于生成向量嵌入的模型"
                      initialValue="default"
                      rules={[{ required: true, message: '请选择嵌入模型' }]}
                      style={{ marginBottom: 0 }}
                    >
                      <Select
                        placeholder="选择嵌入模型"
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          option?.label?.toLowerCase().includes(input.toLowerCase())
                        }
                        style={{ borderRadius: '6px' }}
                        options={[
                          // 默认模型选项
                          {
                            value: 'default',
                            label: `默认嵌入模型${defaultEmbeddingModel ? ` (${embeddingModels.find(m => m.id === defaultEmbeddingModel)?.name || 'Unknown'})` : ''}`,
                            isDefault: true,
                            model: defaultEmbeddingModel ? embeddingModels.find(m => m.id === defaultEmbeddingModel) : null
                          },
                          // 其他模型选项
                          ...(embeddingModels && embeddingModels.length > 0 ?
                            embeddingModels.map(model => ({
                              value: model.id.toString(),
                              label: `${model.name} (${model.provider})`,
                              isDefault: false,
                              model: model
                            })) : []
                          )
                        ]}
                        optionRender={(option) => {
                          if (option.data.isDefault) {
                            return (
                              <div>
                                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                  <span style={{ fontWeight: 'bold' }}>默认嵌入模型</span>
                                  <Tag color="green" size="small">默认</Tag>
                                </div>
                                {option.data.model && (
                                  <div style={{ fontSize: '12px', color: '#666' }}>
                                    {option.data.model.provider} - {option.data.model.model_id}
                                  </div>
                                )}
                              </div>
                            );
                          } else {
                            return (
                              <div>
                                <div style={{ fontWeight: 'bold' }}>{option.data.model.name}</div>
                                <div style={{ fontSize: '12px', color: '#666' }}>
                                  {option.data.model.provider} - {option.data.model.model_id}
                                </div>
                              </div>
                            );
                          }
                        }}
                      />
                    </Form.Item>

                    {/* 嵌入维度 */}
                    <Form.Item
                      name={['framework_config', 'embedding_dimension']}
                      label={
                        <Space>
                          <SettingOutlined style={{ color: '#722ed1' }} />
                          <span>嵌入维度</span>
                          <Tooltip title="嵌入向量的维度大小。不同模型有不同的默认维度，如text-embedding-3-small为1536，bge-m3为1024等。">
                            <InfoCircleOutlined style={{ color: '#1890ff' }} />
                          </Tooltip>
                        </Space>
                      }
                      tooltip="嵌入向量的维度大小"
                      initialValue={1024}
                      style={{ marginBottom: 16 }}
                    >
                      <InputNumber
                        min={128}
                        max={4096}
                        step={1}
                        placeholder="如：1536, 1024, 768"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Card>
                </Col>
              </Row>

              {/* 记忆分区配置 */}
              <Card
                type="inner"
                title={
                  <Space>
                    <ShareAltOutlined />
                    记忆分区配置
                    <Tooltip title="Graphiti支持按任务、会话或混合方式组织记忆数据，实现高效的记忆检索和管理。时间衰减功能可以让较旧的记忆随时间逐渐降低重要性。">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                style={{ marginBottom: 16 }}
              >


                    <Form.Item
                      name={['framework_config', 'memory_partition_strategy']}
                      label="分区策略"
                      initialValue="by_task"
                      tooltip="决定如何组织记忆数据"
                      style={{ marginBottom: 12 }}
                    >
                      <Select placeholder="选择分区策略">
                        <Select.Option value="by_task">按任务分区</Select.Option>
                        <Select.Option value="by_conversation">按会话分区</Select.Option>
                        <Select.Option value="hybrid">混合分区</Select.Option>
                      </Select>
                    </Form.Item>

                    <Row gutter={8}>
                      <Col span={12}>
                        <Form.Item
                          name={['framework_config', 'max_nodes_per_partition']}
                          label="每分区最大节点数"
                          initialValue={10000}
                          tooltip="限制每个分区的最大节点数量，防止分区过大"
                          style={{ marginBottom: 12 }}
                        >
                          <InputNumber min={1000} max={100000} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={['framework_config', 'importance_threshold']}
                          label="重要性阈值"
                          initialValue={0.3}
                          tooltip="低于此阈值的记忆可能会被过滤掉，范围0-1"
                          style={{ marginBottom: 12 }}
                        >
                          <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name={['framework_config', 'enable_time_decay']}
                          label="启用时间衰减"
                          valuePropName="checked"
                          initialValue={true}
                          tooltip="随时间降低旧记忆的重要性"
                          style={{ marginBottom: 0 }}
                        >
                          <Switch />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={['framework_config', 'use_namespaces']}
                          label="启用图谱命名空间"
                          valuePropName="checked"
                          initialValue={false}
                          tooltip="为不同用户或应用创建隔离的图谱空间"
                          style={{ marginBottom: 0 }}
                        >
                          <Switch />
                        </Form.Item>
                      </Col>
                    </Row>
              </Card>
            </Card>
          )}

          {selectedFramework === 'graphrag' && (
            <Card
              title="GraphRAG 配置"
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'api_key']} label="API Key">
                    <Input.Password placeholder="your-api-key" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'api_base']} label="API Base URL">
                    <Input placeholder="https://api.openai.com/v1" />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'model']} label="模型">
                    <Input placeholder="gpt-4" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['framework_config', 'max_tokens']} label="最大Token数">
                    <InputNumber min={100} max={8000} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {/* 操作按钮区域 */}
          <Card
            title="操作"
            style={{ marginBottom: 24 }}
          >
            {/* Graphiti服务配置 */}
            <Card
              type="inner"
              title={
                <Space>
                  <CloudServerOutlined />
                  Graphiti 容器化服务
                  <Tag color="green" size="small">容器部署</Tag>
                </Space>
              }
              style={{ marginBottom: 16 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name={['framework_config', 'service_url']}
                    label="服务地址"
                    initialValue="http://localhost:8000"
                    rules={[{ required: true, message: '请输入服务地址' }]}
                    tooltip="Graphiti容器化服务的访问地址"
                  >
                    <Input placeholder="http://localhost:8000" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="服务控制" style={{ marginBottom: 0 }}>
                    <Space>
                      <Button
                        type={status?.connected ? "default" : "primary"}
                        icon={status?.connected ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                        size="small"
                        onClick={() => handleServiceControl(status?.connected ? 'stop' : 'start')}
                        loading={loading}
                      >
                        {status?.connected ? '停止服务' : '启动服务'}
                      </Button>
                    </Space>
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            <Space wrap>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
              >
                保存配置
              </Button>

              <Button
                icon={<CodeOutlined />}
                onClick={() => setMcpModalVisible(true)}
              >
                MCP服务器示例
              </Button>
              <Button
                icon={<SearchOutlined />}
                onClick={() => setQueryModalVisible(true)}
              >
                测试查询
              </Button>

              <Button
                danger
                icon={<ClearOutlined />}
                onClick={handleClearData}
                loading={clearLoading}
              >
                清空数据
              </Button>
            </Space>
          </Card>
        </Form>
      )}



      {/* 测试查询Modal */}
      <TestQueryModal
        visible={queryModalVisible}
        onCancel={() => setQueryModalVisible(false)}
        onQuery={handleTestQuery}
        loading={loading}
        result={testResult}
        config={config}
      />

      {/* MCP服务器示例Modal */}
      <Modal
        title={
          <Space>
            MCP服务器集成示例
            <Tooltip title={`基于当前系统配置生成的MCP服务器集成配置。数据库：${config?.framework_config?.database_type || 'Neo4j'}，模型：${config?.framework_config?.text_model_id === 'default' ? '默认模型' : '自定义模型'}`}>
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        }
        open={mcpModalVisible}
        onCancel={() => setMcpModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setMcpModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <MCPServerExample config={config} />
      </Modal>
    </div>
  );
};

// 测试查询Modal组件
const TestQueryModal = ({ visible, onCancel, onQuery, loading, result, config }) => {
  const [queryForm] = Form.useForm();

  const handleQuery = () => {
    queryForm.validateFields().then(values => {
      onQuery(values);
    });
  };

  return (
    <Modal
      title="图谱查询测试"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
        <Button
          key="query"
          type="primary"
          onClick={handleQuery}
          loading={loading}
        >
          执行查询
        </Button>
      ]}
    >
      <Form
        form={queryForm}
        layout="vertical"
        initialValues={{
          mode: config?.default_query_mode || 'hybrid',
          top_k: config?.top_k || 60,
          chunk_top_k: config?.chunk_top_k || 10,
          response_type: 'Multiple Paragraphs'
        }}
      >
        <Form.Item
          name="query"
          label="查询内容"
          rules={[{ required: true, message: '请输入查询内容' }]}
        >
          <TextArea
            rows={3}
            placeholder="请输入要查询的内容..."
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="mode" label="查询模式">
              <Radio.Group size="small">
                <Radio.Button value="hybrid">Hybrid</Radio.Button>
                <Radio.Button value="local">Local</Radio.Button>
                <Radio.Button value="global">Global</Radio.Button>
                <Radio.Button value="mix">Mix</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="top_k" label="Top-K">
              <InputNumber min={1} max={200} size="small" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="response_type" label="响应类型">
              <Radio.Group size="small">
                <Radio.Button value="Multiple Paragraphs">多段落</Radio.Button>
                <Radio.Button value="Single Paragraph">单段落</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {result && (
        <div style={{ marginTop: 16 }}>
          <Divider>查询结果</Divider>
          <Alert
            message={
              <Space>
                <Text strong>响应时间:</Text> {result.response_time?.toFixed(2)}s
                <Text strong>查询模式:</Text> {result.query_params?.mode}
                <Text strong>框架:</Text> {result.framework}
              </Space>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
          <Card size="small">
            <Paragraph>
              {result.result}
            </Paragraph>
          </Card>
        </div>
      )}
    </Modal>
  );
};

// MCP服务器示例组件
const MCPServerExample = ({ config }) => {
  const [envConfig, setEnvConfig] = useState({});
  const [loading, setLoading] = useState(true);

  // 生成环境变量配置（基于model-config）
  const generateEnvConfig = async () => {
    try {
      setLoading(true);
      const frameworkConfig = config?.framework_config || {};
      const dbType = frameworkConfig.database_type || 'neo4j';

      let newEnvConfig = {};

      // 获取文本生成模型配置（优先使用用户指定的模型）
      try {
        let textModel = null;

        // 检查用户是否指定了自定义文本生成模型
        if (frameworkConfig.text_model_id && frameworkConfig.text_model_id !== 'default') {
          // 使用用户指定的模型
          textModel = { id: frameworkConfig.text_model_id };
        } else {
          // 使用系统默认模型
          const defaults = await modelConfigAPI.getDefaults();
          textModel = defaults.text_model;
        }

        if (textModel) {
          // 获取模型详细配置（包含API Key）
          const modelDetail = await modelConfigAPI.getById(textModel.id, true);

          // 根据提供商设置环境变量
          newEnvConfig["OPENAI_API_KEY"] = modelDetail.api_key || "no-api-key";
          newEnvConfig["MODEL_NAME"] = modelDetail.model_id || "gpt-4o-mini";
          newEnvConfig["SMALL_MODEL_NAME"] = modelDetail.model_id || "gpt-4o-mini";

          // 如果有base_url，总是包含它
          if (modelDetail.base_url) {
            newEnvConfig["OPENAI_BASE_URL"] = modelDetail.base_url;
          } else if (modelDetail.provider === 'aliyun') {
            // 阿里云默认base_url
            newEnvConfig["OPENAI_BASE_URL"] = "https://dashscope.aliyuncs.com/compatible-mode/v1";
          } else if (modelDetail.provider === 'zhipu') {
            // 智谱默认base_url
            newEnvConfig["OPENAI_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4";
          }
        } else {
          // 没有默认模型
          newEnvConfig["OPENAI_API_KEY"] = "请先在模型配置中设置默认文本生成模型";
          newEnvConfig["MODEL_NAME"] = "gpt-4o-mini";
          newEnvConfig["SMALL_MODEL_NAME"] = "gpt-4o-mini";
        }

        // 获取嵌入模型配置（优先使用用户指定的模型）
        let embeddingModel = null;

        // 检查用户是否指定了自定义嵌入模型
        if (frameworkConfig.embedding_model_id && frameworkConfig.embedding_model_id !== 'default') {
          // 使用用户指定的嵌入模型
          embeddingModel = { id: frameworkConfig.embedding_model_id };
        } else {
          // 使用系统默认嵌入模型
          const defaults = await modelConfigAPI.getDefaults();
          embeddingModel = defaults.embedding_model;
        }

        if (embeddingModel) {
          // 获取嵌入模型详细配置
          const embeddingDetail = await modelConfigAPI.getById(embeddingModel.id, true);

          // 设置嵌入模型环境变量，如果嵌入模型没有API Key，使用"no-api-key"占位符
          newEnvConfig["OPENAI_EMBEDDER_API_KEY"] = embeddingDetail.api_key || "no-api-key";
          newEnvConfig["OPENAI_EMBEDDER_MODEL_ID"] = embeddingDetail.model_id || "text-embedding-3-small";

          // 设置嵌入维度，根据模型类型设置默认值
          const defaultDimension = embeddingDetail.model_id?.includes('nomic-embed') ? '768' :
                                   embeddingDetail.model_id?.includes('bge-m3') ? '1024' :
                                   embeddingDetail.model_id?.includes('text-embedding-3-large') ? '3072' :
                                   embeddingDetail.model_id?.includes('text-embedding-3-small') ? '1536' : '1536';
          newEnvConfig["OPENAI_EMBEDDER_DIMENSION"] = embeddingDetail.dimension?.toString() || defaultDimension;

          // 如果有base_url，设置嵌入模型的base_url，否则使用文本生成模型的base_url
          if (embeddingDetail.base_url) {
            newEnvConfig["OPENAI_EMBEDDER_API_URL"] = embeddingDetail.base_url;
          } else if (newEnvConfig["OPENAI_BASE_URL"]) {
            newEnvConfig["OPENAI_EMBEDDER_API_URL"] = newEnvConfig["OPENAI_BASE_URL"];
          }
        } else {
          // 没有默认嵌入模型，使用文本生成模型的配置作为回退
          newEnvConfig["OPENAI_EMBEDDER_API_KEY"] = newEnvConfig["OPENAI_API_KEY"];
          newEnvConfig["OPENAI_EMBEDDER_MODEL_ID"] = "text-embedding-3-small";
          newEnvConfig["OPENAI_EMBEDDER_DIMENSION"] = "1536"; // text-embedding-3-small的默认维度
          if (newEnvConfig["OPENAI_BASE_URL"]) {
            newEnvConfig["OPENAI_EMBEDDER_API_URL"] = newEnvConfig["OPENAI_BASE_URL"];
          }
        }
      } catch (error) {
        console.error('获取默认模型配置失败:', error);
        newEnvConfig["OPENAI_API_KEY"] = "获取模型配置失败，请检查模型配置";
        newEnvConfig["MODEL_NAME"] = "gpt-4o-mini";
      }

      // 数据库配置
      if (dbType === 'neo4j') {
        newEnvConfig = {
          ...newEnvConfig,
          "NEO4J_URI": frameworkConfig.neo4j_uri || "bolt://localhost:7687",
          "NEO4J_USER": frameworkConfig.neo4j_user || "neo4j",
          "NEO4J_PASSWORD": frameworkConfig.neo4j_password || "请在图谱增强配置中设置Neo4j密码"
        };
      } else if (dbType === 'falkordb') {
        const host = frameworkConfig.falkordb_host || "localhost";
        const port = frameworkConfig.falkordb_port || 6379;
        newEnvConfig = {
          ...newEnvConfig,
          "FALKORDB_HOST": host,
          "FALKORDB_PORT": port.toString()
        };
        if (frameworkConfig.falkordb_password) {
          newEnvConfig["FALKORDB_PASSWORD"] = frameworkConfig.falkordb_password;
        }
        if (frameworkConfig.falkordb_username) {
          newEnvConfig["FALKORDB_USERNAME"] = frameworkConfig.falkordb_username;
        }
      }

      // 添加OpenAI兼容标记
      if (frameworkConfig.openai_compatible) {
        newEnvConfig["OPENAI_COMPATIBLE"] = "true";
      } else {
        newEnvConfig["OPENAI_COMPATIBLE"] = "false";
      }

      setEnvConfig(newEnvConfig);
    } catch (error) {
      console.error('生成环境配置失败:', error);
      setEnvConfig({
        "OPENAI_API_KEY": "配置生成失败",
        "MODEL_NAME": "gpt-4o-mini",
        "SMALL_MODEL_NAME": "gpt-4o-mini"
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时生成配置
  useEffect(() => {
    generateEnvConfig();
  }, [config]);

  // MCP配置
  const mcpConfig = {
    "mcpServers": {
      "graphiti-memory-test": {
        "transport": "stdio",
        "command": "uv",
        "args": [
          "run",
          "--directory",
          "/path/to/graphiti/mcp_server",
          "--isolated",
          "graphiti_mcp_server.py",
          "--transport",
          "stdio",
          "--group-id",
          "default"
        ],
        "env": envConfig
      }
    }
  };

  // 启动命令
  const startCommand = `# 克隆Graphiti仓库
git clone https://github.com/getzep/graphiti.git
cd graphiti/mcp_server

# 安装依赖（包含可选依赖）
uv sync --all-extras

# 启动MCP服务器（stdio模式）
uv run --isolated graphiti_mcp_server.py \\
  --transport stdio \\
  --group-id default`;



  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '10px' }}>正在生成MCP配置...</div>
      </div>
    );
  }

  return (
    <div>
      <Typography.Title level={5}>
        <Space>
          1. 安装和启动MCP服务器
          <Tooltip title="请确保已安装Python 3.10+和uv包管理器">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      </Typography.Title>
      <Typography.Text code copyable={{ text: startCommand }}>
        点击复制启动命令
      </Typography.Text>
      <pre style={{
        background: '#f5f5f5',
        padding: '12px',
        borderRadius: '4px',
        fontSize: '12px',
        marginBottom: '16px'
      }}>
        {startCommand}
      </pre>

      <Typography.Title level={5}>
        <Space>
          2. MCP配置文件
          <Tooltip title={`配置基于当前系统的默认模型配置和图谱增强配置自动生成。API Key从默认文本生成模型配置中读取，数据库配置从图谱增强设置中读取。确保图数据库（${config?.framework_config?.database_type || 'Neo4j'}）正在运行。`}>
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
        </Space>
      </Typography.Title>
      <Typography.Text code copyable={{ text: JSON.stringify(mcpConfig, null, 2) }}>
        点击复制MCP配置
      </Typography.Text>
      <pre style={{
        background: '#f5f5f5',
        padding: '12px',
        borderRadius: '4px',
        fontSize: '12px',
        overflow: 'auto',
        maxHeight: '400px',
        marginBottom: '16px'
      }}>
        {JSON.stringify(mcpConfig, null, 2)}
      </pre>


      <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
        <Space>
          <span>💡 提示：</span>
          <Tooltip title="group_id设置为'default'，可根据任务需要修改为'task_123'等。配置中的Graphiti路径需要根据实际安装位置调整。如果API Key显示错误信息，请检查模型配置页面的默认模型设置。">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
          </Tooltip>
          <span>悬停查看配置注意事项</span>
        </Space>
      </div>
    </div>
  );
};

export default GraphEnhancementSettingsPage;
